from django.shortcuts import render, redirect
from django.db.models import Sum, F, Q
from .models import Product, StockTransaction, StockDetail
from .serializers import ProductSerializer, StockTransactionSerializer
from rest_framework.decorators import api_view
from rest_framework.response import Response
from rest_framework import status
from .forms import StockTransactionForm
from .forms import StockTransactionForm
from django.contrib import messages

# API: Get current inventory
@api_view(['GET'])
def inventory_api(request):
    products = Product.objects.all()
    result = []

    for product in products:
        in_qty = StockDetail.objects.filter(product=product, transaction__transaction_type='IN').aggregate(Sum('quantity'))['quantity__sum'] or 0
        out_qty = StockDetail.objects.filter(product=product, transaction__transaction_type='OUT').aggregate(Sum('quantity'))['quantity__sum'] or 0
        available = in_qty - out_qty

        result.append({
            'product': product.name,
            'sku': product.sku,
            'available_quantity': available
        })

    return Response(result)


# HTML view: inventory list
def inventory_view(request):
    products = Product.objects.all()
    data = []

    for product in products:
        in_qty = StockDetail.objects.filter(product=product, transaction__transaction_type='IN').aggregate(Sum('quantity'))['quantity__sum'] or 0
        out_qty = StockDetail.objects.filter(product=product, transaction__transaction_type='OUT').aggregate(Sum('quantity'))['quantity__sum'] or 0
        available = in_qty - out_qty

        data.append({
            'product': product,
            'available_quantity': available
        })

    return render(request, 'inventory_list.html', {'inventory': data})

def add_transaction(request):
    if request.method == 'POST':
        form = StockTransactionForm(request.POST)
        if form.is_valid():
            transaction_type = form.cleaned_data['transaction_type']
            product = form.cleaned_data['product']
            quantity = form.cleaned_data['quantity']

            # Validate stock for OUT transaction
            if transaction_type == 'OUT':
                in_qty = StockDetail.objects.filter(product=product, transaction__transaction_type='IN').aggregate(Sum('quantity'))['quantity__sum'] or 0
                out_qty = StockDetail.objects.filter(product=product, transaction__transaction_type='OUT').aggregate(Sum('quantity'))['quantity__sum'] or 0
                available = in_qty - out_qty
                if quantity > available:
                    messages.error(request, f"❌ Insufficient stock for {product.name}. Only {available} available.")
                    return redirect('add_transaction')

            # Save the transaction
            tx = StockTransaction.objects.create(transaction_type=transaction_type)
            StockDetail.objects.create(transaction=tx, product=product, quantity=quantity)
            messages.success(request, f"✅ {transaction_type} transaction added successfully!")
            return redirect('inventory_view')
    else:
        form = StockTransactionForm()

    return render(request, 'transaction_form.html', {'form': form})
