# Warehouse Inventory Management System

A Django-based warehouse inventory management system that tracks stock movements and shows inventory levels in real-time.

## Features

- **Product Management**: Add and delete products with SKU tracking
- **Stock Transactions**: Record IN/OUT stock movements
- **Real-time Inventory**: View current stock levels
- **REST API**: JSON API endpoints for inventory data
- **Input Validation**: Prevents overselling and invalid transactions
- **Clean UI**: Simple, responsive web interface

## Database Schema

- **Product (prodmast)**: Stores product details (name, SKU, description)
- **StockTransaction (stckmain)**: Stores transaction details (type, timestamp)
- **StockDetail (stckdetail)**: Stores product quantities within each transaction

## API Endpoints

- `GET /api/inventory/` - Returns current inventory levels in JSON format
- `GET /` - Main inventory dashboard (HTML)
- `GET /add-product/` - Add new product form
- `GET /add/` - Add stock transaction form
- `GET /delete-product/<id>/` - Delete product confirmation

## Installation & Setup

1. Clone the repository
2. Install dependencies: `pip install -r requirements.txt`
3. Run migrations: `python manage.py migrate`
4. Start server: `python manage.py runserver`

## Usage

1. **Add Products**: Click "➕ Add Product" to create new products
2. **Stock Transactions**: Click "📦 Add Stock Transaction" to add/remove stock
3. **View Inventory**: Main page shows current stock levels
4. **Delete Products**: Click "🗑️ Delete" (only allowed if no transactions exist)

## Validation Features

- Prevents negative stock (OUT transactions cannot exceed available stock)
- SKU uniqueness validation
- Required field validation
- Cannot delete products with existing transactions

## Technology Stack

- **Backend**: Django 5.2.4
- **API**: Django REST Framework
- **Database**: SQLite (development)
- **Frontend**: HTML/CSS with Django templates
- **Deployment**: Gunicorn + Whitenoise

## Live Demo

[Add your deployed URL here]

---

Built for warehouse inventory management with clean code architecture and proper validation.
