<!DOCTYPE html>
<html>
<head>
    <title>Add Product - Warehouse Inventory</title>
    <style>
        body { font-family: Arial, sans-serif; margin: 40px; }
        .form-container { max-width: 500px; margin: 0 auto; }
        .form-group { margin-bottom: 15px; }
        label { display: block; margin-bottom: 5px; font-weight: bold; }
        input, textarea, select { width: 100%; padding: 8px; border: 1px solid #ddd; border-radius: 4px; }
        button { background-color: #007bff; color: white; padding: 10px 20px; border: none; border-radius: 4px; cursor: pointer; }
        button:hover { background-color: #0056b3; }
        .back-link { color: #007bff; text-decoration: none; margin-bottom: 20px; display: inline-block; }
        .back-link:hover { text-decoration: underline; }
        .messages { margin-bottom: 20px; }
        .alert { padding: 10px; border-radius: 4px; margin-bottom: 10px; }
        .alert-success { background-color: #d4edda; color: #155724; border: 1px solid #c3e6cb; }
        .alert-error { background-color: #f8d7da; color: #721c24; border: 1px solid #f5c6cb; }
    </style>
</head>
<body>
    <div class="form-container">
        <a href="/" class="back-link">← Back to Inventory</a>
        
        <h1>Add New Product</h1>
        
        {% if messages %}
            <div class="messages">
                {% for message in messages %}
                    <div class="alert alert-{{ message.tags }}">{{ message }}</div>
                {% endfor %}
            </div>
        {% endif %}
        
        <form method="post">
            {% csrf_token %}
            
            <div class="form-group">
                <label for="{{ form.name.id_for_label }}">Product Name:</label>
                {{ form.name }}
                {% if form.name.errors %}
                    <div style="color: red; font-size: 12px;">{{ form.name.errors }}</div>
                {% endif %}
            </div>
            
            <div class="form-group">
                <label for="{{ form.sku.id_for_label }}">SKU (Stock Keeping Unit):</label>
                {{ form.sku }}
                {% if form.sku.errors %}
                    <div style="color: red; font-size: 12px;">{{ form.sku.errors }}</div>
                {% endif %}
            </div>
            
            <div class="form-group">
                <label for="{{ form.description.id_for_label }}">Description:</label>
                {{ form.description }}
                {% if form.description.errors %}
                    <div style="color: red; font-size: 12px;">{{ form.description.errors }}</div>
                {% endif %}
            </div>
            
            <button type="submit">Add Product</button>
        </form>
    </div>
</body>
</html>
