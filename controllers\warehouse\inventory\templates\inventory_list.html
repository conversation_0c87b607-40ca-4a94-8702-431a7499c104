<!DOCTYPE html>
<html>
<head>
    <title>Warehouse Inventory</title>
    <style>
        body { font-family: Arial, sans-serif; margin: 40px; }
        table { border-collapse: collapse; width: 60%; }
        th, td { padding: 8px 12px; border: 1px solid #ddd; }
        th { background: #eee; }
        .actions { margin: 20px 0; }
        .btn { display: inline-block; padding: 10px 15px; margin-right: 10px; text-decoration: none; border-radius: 4px; }
        .btn-primary { background-color: #007bff; color: white; }
        .btn-success { background-color: #28a745; color: white; }
        .btn-danger { background-color: #dc3545; color: white; }
        .btn-small { padding: 5px 10px; font-size: 12px; }
        .btn:hover { opacity: 0.8; }
        .messages { margin-bottom: 20px; }
        .alert { padding: 10px; border-radius: 4px; margin-bottom: 10px; }
        .alert-success { background-color: #d4edda; color: #155724; border: 1px solid #c3e6cb; }
        .alert-error { background-color: #f8d7da; color: #721c24; border: 1px solid #f5c6cb; }
    </style>
</head>
<body>
    <h2>📦 Warehouse Inventory</h2>

    {% if messages %}
        <div class="messages">
            {% for message in messages %}
                <div class="alert alert-{{ message.tags }}">{{ message }}</div>
            {% endfor %}
        </div>
    {% endif %}

    <div class="actions">
        <a href="/add-product/" class="btn btn-success">➕ Add Product</a>
        <a href="/add/" class="btn btn-primary">📦 Add Stock Transaction</a>
    </div>

    <table>
        <tr>
            <th>Product</th>
            <th>SKU</th>
            <th>Available</th>
            <th>Actions</th>
        </tr>
        {% for item in inventory %}
        <tr>
            <td>{{ item.product.name }}</td>
            <td>{{ item.product.sku }}</td>
            <td>{{ item.available_quantity }}</td>
            <td>
                <a href="/delete-product/{{ item.product.id }}/" class="btn btn-danger btn-small">🗑️ Delete</a>
            </td>
        </tr>
        {% empty %}
        <tr>
            <td colspan="4" style="text-align: center; color: #666;">No products found. <a href="/add-product/">Add your first product</a></td>
        </tr>
        {% endfor %}
    </table>
</body>
</html>
