<!DOCTYPE html>
<html>
<head>
    <title>Delete Product - Warehouse Inventory</title>
    <style>
        body { font-family: Arial, sans-serif; margin: 40px; }
        .container { max-width: 500px; margin: 0 auto; }
        .warning { background-color: #fff3cd; border: 1px solid #ffeaa7; color: #856404; padding: 15px; border-radius: 4px; margin-bottom: 20px; }
        .danger { background-color: #f8d7da; border: 1px solid #f5c6cb; color: #721c24; padding: 15px; border-radius: 4px; margin-bottom: 20px; }
        .product-info { background-color: #f8f9fa; padding: 15px; border-radius: 4px; margin-bottom: 20px; }
        .btn { display: inline-block; padding: 10px 20px; margin-right: 10px; text-decoration: none; border-radius: 4px; border: none; cursor: pointer; }
        .btn-danger { background-color: #dc3545; color: white; }
        .btn-secondary { background-color: #6c757d; color: white; }
        .btn:hover { opacity: 0.8; }
        .back-link { color: #007bff; text-decoration: none; margin-bottom: 20px; display: inline-block; }
        .back-link:hover { text-decoration: underline; }
    </style>
</head>
<body>
    <div class="container">
        <a href="/" class="back-link">← Back to Inventory</a>
        
        <h1>Delete Product</h1>
        
        <div class="product-info">
            <h3>{{ product.name }}</h3>
            <p><strong>SKU:</strong> {{ product.sku }}</p>
            <p><strong>Description:</strong> {{ product.description|default:"No description" }}</p>
        </div>
        
        {% if has_transactions %}
            <div class="danger">
                <strong>⚠️ Cannot Delete Product</strong><br>
                This product has stock transactions associated with it. You must remove all stock transactions before deleting this product.
            </div>
            <a href="/" class="btn btn-secondary">← Back to Inventory</a>
        {% else %}
            <div class="warning">
                <strong>⚠️ Warning</strong><br>
                Are you sure you want to delete this product? This action cannot be undone.
            </div>
            
            <form method="post" style="display: inline;">
                {% csrf_token %}
                <button type="submit" class="btn btn-danger">🗑️ Yes, Delete Product</button>
            </form>
            <a href="/" class="btn btn-secondary">Cancel</a>
        {% endif %}
    </div>
</body>
</html>
