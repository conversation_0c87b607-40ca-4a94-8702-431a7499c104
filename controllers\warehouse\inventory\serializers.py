from rest_framework import serializers

from .models import Product, StockTransaction, StockDetail

class ProductSerializer(serializers.ModelSerializer):
    class Meta:
        model = Product
        fields = '__all__'


class StockDetailSerializer(serializers.ModelSerializer):
    class Meta:
        model = StockDetail
        fields = '__all__'


class StockTransactionSerializer(serializers.ModelSerializer):
    details = StockDetailSerializer(many=True, write_only=True)

    class Meta:
        model = StockTransaction
        fields = ['id', 'transaction_type', 'timestamp', 'details']

    def create(self, validated_data):
        details_data = validated_data.pop('details')
        transaction = StockTransaction.objects.create(**validated_data)

        for item in details_data:
            product = item['product']
            quantity = item['quantity']

            if validated_data['transaction_type'] == 'OUT':
                # check if stock is sufficient
                in_qty = StockDetail.objects.filter(product=product, transaction__transaction_type='IN').aggregate(models.Sum('quantity'))['quantity__sum'] or 0
                out_qty = StockDetail.objects.filter(product=product, transaction__transaction_type='OUT').aggregate(models.Sum('quantity'))['quantity__sum'] or 0
                available = in_qty - out_qty
                if quantity > available:
                    raise serializers.ValidationError(f"Insufficient stock for product: {product.name}")

            StockDetail.objects.create(transaction=transaction, **item)

        return transaction
